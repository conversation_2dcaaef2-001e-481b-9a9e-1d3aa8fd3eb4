enum AnalyticsKeyScope {
  query
  send
}

type Authors implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: AuthorsItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [AuthorsItem!]!
}

""""""
type AuthorsItem implements BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
  avatar: BlockImage!
  xUrl: String
}

input AuthorsItemFilterInput {
  AND: AuthorsItemFilterInput
  OR: AuthorsItemFilterInput
  _id: StringFilter
  _slug: StringFilter
  _sys_apiNamePath: StringFilter
  _sys_createdAt: DateFilter
  _sys_hash: StringFilter
  _sys_id: StringFilter
  _sys_idPath: StringFilter
  _sys_lastModifiedAt: DateFilter
  _sys_slug: StringFilter
  _sys_slugPath: StringFilter
  _sys_title: StringFilter
  _title: StringFilter
  xUrl: StringFilter
}

enum AuthorsItemOrderByEnum {
  _sys_createdAt__ASC
  _sys_createdAt__DESC
  _sys_hash__ASC
  _sys_hash__DESC
  _sys_id__ASC
  _sys_id__DESC
  _sys_lastModifiedAt__ASC
  _sys_lastModifiedAt__DESC
  _sys_slug__ASC
  _sys_slug__DESC
  _sys_title__ASC
  _sys_title__DESC
  avatar__ASC
  avatar__DESC
  xUrl__ASC
  xUrl__DESC
}

"""
{"schemaType":"({\n  name: string;\n  required: boolean;\n  placeholder?: string;\n  defaultValue?: string;\n  helpText?: string\n} & {\n  id: string;\n  label: string\n} & ({\n  type: \"text\" | \"textarea\" | \"number\" | \"date\" | \"datetime\" | \"email\" | \"checkbox\" | \"hidden\"\n} | {\n  type: \"select\" | \"radio\";\n  options: string[];\n  multiple: boolean\n} | {\n  type: \"file\";\n  private: boolean\n}))[]"}
"""
scalar BSHBEventSchema

"""{"schemaType":"RichTextNode[]"}"""
scalar BSHBRichTextContentSchema

"""{"schemaType":"RichTextTocNode[]"}"""
scalar BSHBRichTextTOCSchema

type BaseRichTextJson implements RichTextJson {
  blocks: String!
  content: BSHBRichTextContentSchema!
  toc: BSHBRichTextTOCSchema!
}

type BlockAudio implements MediaBlock {
  """
  The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size.
  """
  duration: Float!
  fileName: String!
  fileSize: Int!
  lastModified: Float!
  mimeType: String!
  url: String!
}

type BlockCodeSnippet {
  allowedLanguages: [CodeSnippetLanguage!]!
  code: String!
  html(
    """Theme for the code snippet"""
    theme: String = "github-dark"
  ): String! @deprecated(reason: "Figuring out the correct api.")
  language: CodeSnippetLanguage!
}

type BlockColor {
  b: Int!
  g: Int!
  hex: String!
  hsl: String!
  r: Int!
  rgb: String!
}

interface BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
}

type BlockDocumentSys {
  apiNamePath: String!
  createdAt: String!
  hash: String!
  id: ID!
  idPath: String!
  lastModifiedAt: String!
  slug: String!
  slugPath: String!
  title: String!
}

type BlockFile implements MediaBlock {
  fileName: String!
  fileSize: Int!
  lastModified: Float!
  mimeType: String!
  url: String!
}

type BlockImage implements MediaBlock {
  alt: String
  aspectRatio: String!
  blurDataURL: String!
  fileName: String!
  fileSize: Int!
  height: Int!
  lastModified: Float!
  mimeType: String!
  placeholderURL: String! @deprecated(reason: "Renamed to `blurDataURL` to match Next.js Image's naming convention.")
  rawUrl: String! @deprecated(reason: "Use `url` instead.")
  thumbhash: String!

  "This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:\n\n```graphql\n{\n  imageBlock {\n    url(width: 200, height: 200)\n  }\n}\n```\n\nThis will return the URL with the width and height set to 200 pixels.\n\nBaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).\n"
  url(anim: String, background: String, blur: Int, border: String, brightness: Int, compression: String, contrast: Int, dpr: Int, fit: String, format: String, gamma: String, gravity: String, height: Int, metadata: String, quality: Int, rotate: String, sharpen: String, trim: String, width: Int): String!
  width: Int!
}

interface BlockList implements BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
}

type BlockOgImage {
  height: Int!
  url: String!
  width: Int!
}

"""Rich text block"""
interface BlockRichText {
  html(
    """
    It automatically generates a unique id for each heading present in the HTML. Enabled by default.
    """
    slugs: Boolean = true

    """Inserts a table of contents at the beginning of the HTML."""
    toc: Boolean = false
  ): String!
  json: RichTextJson!
  markdown: String!
  plainText: String!
  readingTime(
    """Words per minute, defaults to average 183wpm"""
    wpm: Int = 183
  ): Int!
}

type BlockVideo implements MediaBlock {
  aspectRatio: String!

  """
  The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size.
  """
  duration: Float!
  fileName: String!
  fileSize: Int!
  height: Int!
  lastModified: Float!
  mimeType: String!
  url: String!
  width: Int!
}

type Blog implements BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
  authors(
    """Filter by a field."""
    filter: AuthorsItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: AuthorsItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): Authors!
  categories(
    """Filter by a field."""
    filter: CategoriesItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: CategoriesItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): Categories!
  posts(
    """Filter by a field."""
    filter: PostsItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: PostsItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): Posts!
}

type Body implements BlockRichText {
  html(
    """
    It automatically generates a unique id for each heading present in the HTML. Enabled by default.
    """
    slugs: Boolean = true

    """Inserts a table of contents at the beginning of the HTML."""
    toc: Boolean = false
  ): String!
  json: BodyRichText!
  markdown: String!
  plainText: String!
  readingTime(
    """Words per minute, defaults to average 183wpm"""
    wpm: Int = 183
  ): Int!
}

type BodyRichText implements RichTextJson {
  content: BSHBRichTextContentSchema!
  toc: BSHBRichTextTOCSchema!
}

type Body_1 implements BlockRichText {
  html(
    """
    It automatically generates a unique id for each heading present in the HTML. Enabled by default.
    """
    slugs: Boolean = true

    """Inserts a table of contents at the beginning of the HTML."""
    toc: Boolean = false
  ): String!
  json: Body_1RichText!
  markdown: String!
  plainText: String!
  readingTime(
    """Words per minute, defaults to average 183wpm"""
    wpm: Int = 183
  ): Int!
}

type Body_1RichText implements RichTextJson {
  content: BSHBRichTextContentSchema!
  toc: BSHBRichTextTOCSchema!
}

type Categories implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: CategoriesItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [CategoriesItem!]!
}

""""""
type CategoriesItem implements BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
}

input CategoriesItemFilterInput {
  AND: CategoriesItemFilterInput
  OR: CategoriesItemFilterInput
  _id: StringFilter
  _slug: StringFilter
  _sys_apiNamePath: StringFilter
  _sys_createdAt: DateFilter
  _sys_hash: StringFilter
  _sys_id: StringFilter
  _sys_idPath: StringFilter
  _sys_lastModifiedAt: DateFilter
  _sys_slug: StringFilter
  _sys_slugPath: StringFilter
  _sys_title: StringFilter
  _title: StringFilter
}

enum CategoriesItemOrderByEnum {
  _sys_createdAt__ASC
  _sys_createdAt__DESC
  _sys_hash__ASC
  _sys_hash__DESC
  _sys_id__ASC
  _sys_id__DESC
  _sys_lastModifiedAt__ASC
  _sys_lastModifiedAt__DESC
  _sys_slug__ASC
  _sys_slug__DESC
  _sys_title__ASC
  _sys_title__DESC
}

"""{"schemaType":"B_Language"}"""
scalar CodeSnippetLanguage

input DateFilter {
  eq: DateTime
  isAfter: DateTime
  isBefore: DateTime
  isNull: Boolean
  neq: DateTime
  onOrAfter: DateTime
  onOrBefore: DateTime
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

type GetUploadSignedURL {
  signedURL: String!
  uploadURL: String!
}

scalar JSON

type LegalPages implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: LegalPagesItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [LegalPagesItem!]!
}

""""""
type LegalPagesItem implements BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
  body: Body_1!
  description: String!
}

input LegalPagesItemFilterInput {
  AND: LegalPagesItemFilterInput
  OR: LegalPagesItemFilterInput
  _id: StringFilter
  _slug: StringFilter
  _sys_apiNamePath: StringFilter
  _sys_createdAt: DateFilter
  _sys_hash: StringFilter
  _sys_id: StringFilter
  _sys_idPath: StringFilter
  _sys_lastModifiedAt: DateFilter
  _sys_slug: StringFilter
  _sys_slugPath: StringFilter
  _sys_title: StringFilter
  _title: StringFilter
  description: StringFilter
}

enum LegalPagesItemOrderByEnum {
  _sys_createdAt__ASC
  _sys_createdAt__DESC
  _sys_hash__ASC
  _sys_hash__DESC
  _sys_id__ASC
  _sys_id__DESC
  _sys_lastModifiedAt__ASC
  _sys_lastModifiedAt__DESC
  _sys_slug__ASC
  _sys_slug__DESC
  _sys_title__ASC
  _sys_title__DESC
  body__ASC
  body__DESC
  description__ASC
  description__DESC
}

input ListFilter {
  isEmpty: Boolean
  length: Int
}

type ListMeta {
  """Number of items after applying filters but before pagination"""
  filteredCount: Int!

  """Total number of items in collection before any filtering/pagination"""
  totalCount: Int!
}

interface MediaBlock {
  fileName: String!
  fileSize: Int!
  lastModified: Float!
  mimeType: String!
  url: String!
}

union MediaBlockUnion = BlockAudio | BlockFile | BlockImage | BlockVideo

type Mutation {
  "Returns a signed url and an upload url so that you can upload files into your repository.\n\nExample usage with JavaScript:\n```js\nasync function handleUpload(file: File) {\n  const { getUploadSignedURL } = await basehub().mutation({\n    getUploadSignedURL: {\n      __args: { fileName: file.name },\n      signedURL: true,\n      uploadURL: true,\n    }\n  })\n\n  const { signedURL, uploadURL } = getUploadSignedURL\n\n  await fetch(signedURL, { method: 'PUT', body: file })\n\n  // done! do something with the uploadURL now\n}\n```\n"
  getUploadSignedURL(
    """SHA256 hash of the file. Used for reusing existing files."""
    fileHash: String

    """The file name"""
    fileName: String!
  ): GetUploadSignedURL!

  """
  Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms.
  """
  transaction(
    """
    The ID of the author of the transaction. If not provided, the API Token will be used.
    """
    authorId: String

    """Auto make a commit in your Repo with the specified message."""
    autoCommit: String

    """Transaction data."""
    data: String!

    """Skip running workflows and event subscribers. Defaults to false."""
    skipWorkflows: Boolean = false

    """Timeout in milliseconds."""
    timeout: Int = 600000
  ): TransactionStatus!

  """
  Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job.
  """
  transactionAsync(
    """
    The ID of the author of the transaction. If not provided, the API Token will be used.
    """
    authorId: String

    """Auto make a commit in your Repo with the specified message."""
    autoCommit: String

    """Transaction data."""
    data: String!

    """Skip running workflows and event subscribers. Defaults to false."""
    skipWorkflows: Boolean = false
  ): String!
  transactionStatus(
    """Transaction ID"""
    id: String!
  ): TransactionStatus!
}

input NumberFilter {
  eq: Float
  gt: Float
  gte: Float
  isNull: Boolean
  lt: Float
  lte: Float
  neq: Float
}

type Posts implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: PostsItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [PostsItem!]!
}

""""""
type PostsItem implements BlockDocument {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
  authors: [AuthorsItem!]!
  body: Body!
  categories: [CategoriesItem!]

  """ISO 8601 date string."""
  date: String!
  description: String!
  image: BlockImage!
}

input PostsItemFilterInput {
  AND: PostsItemFilterInput
  OR: PostsItemFilterInput
  _id: StringFilter
  _slug: StringFilter
  _sys_apiNamePath: StringFilter
  _sys_createdAt: DateFilter
  _sys_hash: StringFilter
  _sys_id: StringFilter
  _sys_idPath: StringFilter
  _sys_lastModifiedAt: DateFilter
  _sys_slug: StringFilter
  _sys_slugPath: StringFilter
  _sys_title: StringFilter
  _title: StringFilter
  authors: PostsItemFilterInput__authors_0___untitled
  categories: PostsItemFilterInput__categories_0___untitled
  date: DateFilter
  description: StringFilter
}

input PostsItemFilterInput__authors_0___untitled {
  _id: StringFilter
  _slug: StringFilter
  _sys_apiNamePath: StringFilter
  _sys_createdAt: DateFilter
  _sys_hash: StringFilter
  _sys_id: StringFilter
  _sys_idPath: StringFilter
  _sys_lastModifiedAt: DateFilter
  _sys_slug: StringFilter
  _sys_slugPath: StringFilter
  _sys_title: StringFilter
  _title: StringFilter
  xUrl: StringFilter
}

input PostsItemFilterInput__categories_0___untitled {
  _id: StringFilter
  _slug: StringFilter
  _sys_apiNamePath: StringFilter
  _sys_createdAt: DateFilter
  _sys_hash: StringFilter
  _sys_id: StringFilter
  _sys_idPath: StringFilter
  _sys_lastModifiedAt: DateFilter
  _sys_slug: StringFilter
  _sys_slugPath: StringFilter
  _sys_title: StringFilter
  _title: StringFilter
}

enum PostsItemOrderByEnum {
  _sys_createdAt__ASC
  _sys_createdAt__DESC
  _sys_hash__ASC
  _sys_hash__DESC
  _sys_id__ASC
  _sys_id__DESC
  _sys_lastModifiedAt__ASC
  _sys_lastModifiedAt__DESC
  _sys_slug__ASC
  _sys_slug__DESC
  _sys_title__ASC
  _sys_title__DESC
  authors__ASC
  authors__DESC
  body__ASC
  body__DESC
  categories__ASC
  categories__DESC
  date__ASC
  date__DESC
  description__ASC
  description__DESC
  image__ASC
  image__DESC
}

type Query {
  _agent(
    """The ID of the agent."""
    id: String!
  ): _AgentSTART

  """Query across the custom AI agents in the repository."""
  _agents: _agents!

  """
  Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key.
  """
  _componentInstances: _components!

  """The structure of the repository. Used by START."""
  _structure(
    """The format of the structure."""
    format: _StructureFormatEnum = xml

    """The format of the structure."""
    resolveTargetsWith: _ResolveTargetsWithEnum

    """A target block to forcefully resolve in the schema."""
    targetBlock: TargetBlock

    """Whether to include constraints in the structure."""
    withConstraints: Boolean = true

    """Whether to include IDs in the structure."""
    withIDs: Boolean = true

    """Whether to include type options in the structure."""
    withTypeOptions: Boolean = true
  ): JSON!
  _sys: RepoSys!
  blog: Blog!
  legalPages(
    """Filter by a field."""
    filter: LegalPagesItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: LegalPagesItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): LegalPages!
}

type RepoSys {
  branches(limit: Int, offset: Int): _Branches!
  hash: String!
  id: ID!
  playgroundInfo: _PlaygroundInfo
  slug: String!
  title: String!
}

interface RichTextJson {
  content: BSHBRichTextContentSchema!
  toc: BSHBRichTextTOCSchema!
}

input SelectFilter {
  excludes: String
  excludesAll: [String!]
  includes: String
  includesAll: [String!]
  includesAny: [String!]
  isEmpty: Boolean
}

input StringFilter {
  contains: String
  endsWith: String
  eq: String
  isNull: Boolean
  matches: StringMatchesFilter
  notEq: String
  startsWith: String
}

input StringMatchesFilter {
  caseSensitive: Boolean = true
  pattern: String!
}

input TargetBlock {
  focus: Boolean
  id: String!
  label: String!
}

type TransactionStatus {
  """Duration in milliseconds."""
  duration: Int
  endedAt: String
  id: String!
  message: String
  startedAt: String!
  status: TransactionStatusEnum!
}

enum TransactionStatusEnum {
  Cancelled
  Completed
  Failed
  Running
  Scheduled
}

type Variant {
  apiName: String!
  color: String!
  id: String!
  isDefault: Boolean!
  label: String!
}

type _AgentSTART implements BlockDocument {
  _agentKey: String!
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!
  accent: String!
  avatar: String!
  chatUrl: String!
  commit: Boolean!
  description: String!
  edit: Boolean!
  embedUrl: String!
  getUserInfo: Boolean!
  grayscale: String!
  manageBranches: Boolean!
  mcpUrl: String!
  model: String!
  searchTheWeb: Boolean!
  slackInstallUrl: String!
  systemPrompt: String!
}

type _BranchInfo {
  archivedAt: String
  archivedBy: String
  authorId: String
  contributors: [String!]
  createdAt: String!
  description: String
  git: _GitInfo
  headCommit: _CommitInfo
  headCommitId: String
  id: ID!
  inlineSuggestionAppliedAt: String
  isDefault: Boolean!
  isInlineSuggestion: Boolean
  name: String!
  playgroundId: String
  rollbackCommitId: String
  rollbackIsoDate: String
  sourceBranchId: String
  updatedAt: String
  workingRootBlockId: String
}

type _Branches {
  _meta: ListMeta!
  items: [_BranchInfo!]!
}

type _CommitInfo {
  authorId: String!
  branchId: String!
  contributors: [String!]
  createdAt: String!
  hash: String!
  id: String!
  mergeParentCommitId: String
  message: String!
  parentCommitId: String

  """Whether this commit is from a playground branch."""
  playgroundId: String
  repoId: String!
  rootBlockId: String!
}

type _GitInfo {
  branch: String!
  deploymentUrl: String
}

type _PlaygroundInfo {
  claimUrl: String
  editUrl: String!
  expiresAt: String
  id: String
}

enum _ResolveTargetsWithEnum {
  id
  objectName
}

enum _StructureFormatEnum {
  json
  xml
}

type _agents {
  start: _AgentSTART!
}

type _components {
  authorsItem(
    """Filter by a field."""
    filter: AuthorsItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: AuthorsItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): authorsItem_AsList!
  categoriesItem(
    """Filter by a field."""
    filter: CategoriesItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: CategoriesItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): categoriesItem_AsList!
  legalPagesItem(
    """Filter by a field."""
    filter: LegalPagesItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: LegalPagesItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): legalPagesItem_AsList!
  postsItem(
    """Filter by a field."""
    filter: PostsItemFilterInput

    """Limit the number of items returned. Defaults to 500."""
    first: Int = 500

    """Order by a field."""
    orderBy: PostsItemOrderByEnum

    """Skip the first n items."""
    skip: Int = 0
  ): postsItem_AsList!
}

type authorsItem_AsList implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: AuthorsItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [AuthorsItem!]!
}

type categoriesItem_AsList implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: CategoriesItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [CategoriesItem!]!
}

type legalPagesItem_AsList implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: LegalPagesItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [LegalPagesItem!]!
}

type postsItem_AsList implements BlockDocument & BlockList {
  _analyticsKey(
    """
    The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
    
    Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
    """
    scope: AnalyticsKeyScope = send
  ): String!
  _dashboardUrl: String!
  _id: String!
  _idPath: String!
  _meta: ListMeta!

  """The key used to search from the frontend."""
  _searchKey: String!
  _slug: String!
  _slugPath: String!
  _sys: BlockDocumentSys!
  _title: String!

  """
  Returns the first item in the list, or null if the list is empty. Useful when you expect only one result.
  """
  item: PostsItem

  """
  Returns the list of items after filtering and paginating according to the arguments sent by the client.
  """
  items: [PostsItem!]!
}