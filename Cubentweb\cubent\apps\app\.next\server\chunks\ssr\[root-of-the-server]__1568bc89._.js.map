{"version": 3, "sources": [], "sections": [{"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'next-forge';\nconst author: Metadata['authors'] = {\n  name: 'Vercel',\n  url: 'https://vercel.com/',\n};\nconst publisher = 'Vercel';\nconst twitterHandle = '@vercel';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/components/sign-in.tsx"], "sourcesContent": ["import { SignIn as ClerkSignIn } from '@clerk/nextjs';\n\ntype SignInProps = {\n  fallbackRedirectUrl?: string;\n  forceRedirectUrl?: string;\n  signUpFallbackRedirectUrl?: string;\n  signUpForceRedirectUrl?: string;\n  // Legacy props for backward compatibility\n  afterSignInUrl?: string;\n  afterSignUpUrl?: string;\n};\n\nexport const SignIn = ({\n  fallbackRedirectUrl,\n  forceRedirectUrl,\n  signUpFallbackRedirectUrl,\n  signUpForceRedirectUrl,\n  afterSignInUrl,\n  afterSignUpUrl\n}: SignInProps) => (\n  <ClerkSignIn\n    fallbackRedirectUrl={fallbackRedirectUrl || afterSignInUrl}\n    forceRedirectUrl={forceRedirectUrl}\n    signUpFallbackRedirectUrl={signUpFallbackRedirectUrl || afterSignUpUrl}\n    signUpForceRedirectUrl={signUpForceRedirectUrl}\n    appearance={{\n      elements: {\n        // Hide only header elements - keep footer for sign up link\n        header: 'hidden',\n        headerTitle: 'hidden',\n        headerSubtitle: 'hidden',\n        // Hide alternative methods and identity preview\n        alternativeMethodsBlockButton: 'hidden',\n        alternativeMethodsBlockButtonText: 'hidden',\n        identityPreview: 'hidden',\n        identityPreviewText: 'hidden',\n        identityPreviewEditButton: 'hidden',\n        // Clean styling with solid white background - ROUNDED modal box with EVEN LESS padding\n        rootBox: 'relative !bg-white !rounded-3xl shadow-2xl min-w-[400px] mx-auto !border-0',\n        card: '!bg-white !shadow-none !border-0 !rounded-3xl p-4 mx-auto',\n        main: '!bg-white !border-0 !shadow-none',\n        modalContent: '!bg-white !border-0 !shadow-none',\n        // Style the form button (Continue button) - reduced height, NO ROUNDING\n        formButtonPrimary: '!bg-gradient-to-r !from-orange-500 !to-orange-600 hover:!from-orange-600 hover:!to-orange-700 !text-white !shadow-md !transition-all !duration-200 !py-1.5 !px-6 !text-base !font-medium !w-full !rounded-none !border-0',\n        // Style Google button with background - FORCE ICON DISPLAY\n        // Style Google button - REDUCED PADDING, FORCE ICON\n        socialButtonsBlockButton: '!bg-slate-50 !border !border-slate-200 hover:!bg-slate-100 !text-gray-700 !shadow-sm hover:!shadow-md !transition-all !duration-200 !py-2 !px-4 !rounded-lg !flex !items-center !justify-center !gap-2 !min-h-[40px]',\n        // Force provider icon to show - TRANSPARENT BACKGROUND\n        socialButtonsProviderIcon: '!inline-flex !w-[18px] !h-[18px] !opacity-100 !visible !bg-transparent !flex-shrink-0',\n        socialButtonsProviderIcon__google: '!inline-flex !w-[18px] !h-[18px] !opacity-100 !visible !bg-transparent !flex-shrink-0',\n        // Style email input - REDUCED PADDING\n        formFieldInput: '!bg-slate-50 !border !border-slate-200 focus:!bg-white focus:!border-orange-500 focus:!ring-2 focus:!ring-orange-500/20 !rounded-lg !py-2 !px-3 !text-base !transition-all !duration-200',\n      },\n    }}\n  />\n);\n"], "names": [], "mappings": ";;;;AAAA;;;AAYO,MAAM,SAAS,CAAC,EACrB,mBAAmB,EACnB,gBAAgB,EAChB,yBAAyB,EACzB,sBAAsB,EACtB,cAAc,EACd,cAAc,EACF,iBACZ,6VAAC,gSAAA,CAAA,SAAW;QACV,qBAAqB,uBAAuB;QAC5C,kBAAkB;QAClB,2BAA2B,6BAA6B;QACxD,wBAAwB;QACxB,YAAY;YACV,UAAU;gBACR,2DAA2D;gBAC3D,QAAQ;gBACR,aAAa;gBACb,gBAAgB;gBAChB,gDAAgD;gBAChD,+BAA+B;gBAC/B,mCAAmC;gBACnC,iBAAiB;gBACjB,qBAAqB;gBACrB,2BAA2B;gBAC3B,uFAAuF;gBACvF,SAAS;gBACT,MAAM;gBACN,MAAM;gBACN,cAAc;gBACd,wEAAwE;gBACxE,mBAAmB;gBACnB,2DAA2D;gBAC3D,oDAAoD;gBACpD,0BAA0B;gBAC1B,uDAAuD;gBACvD,2BAA2B;gBAC3B,mCAAmC;gBACnC,sCAAsC;gBACtC,gBAAgB;YAClB;QACF", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28unauthenticated%29/sign-in/%5B%5B...sign-in%5D%5D/page.tsx"], "sourcesContent": ["import { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport { redirect } from 'next/navigation';\nimport { SignIn } from '@repo/auth/components/sign-in';\n\nconst title = 'Sign in to Cubent.dev';\nconst description = 'Welcome back! Please sign in to continue.';\n\nexport const metadata: Metadata = createMetadata({ title, description });\n\ntype SignInPageProps = {\n  searchParams: Promise<{\n    device_id?: string;\n    state?: string;\n    redirect_url?: string;\n  }>;\n};\n\nconst SignInPage = async ({ searchParams }: SignInPageProps) => {\n  const params = await searchParams;\n\n  // Handle device OAuth flow - redirect to login page with device parameters\n  if (params.device_id && params.state) {\n    redirect(`/login?device_id=${params.device_id}&state=${params.state}`);\n  }\n\n  // Handle redirect_url parameter by updating the fallback redirect URL\n  let fallbackRedirectUrl = '/auth-success';\n  if (params.redirect_url) {\n    fallbackRedirectUrl = `/auth-success?redirect_url=${encodeURIComponent(params.redirect_url)}`;\n  }\n\n  return (\n    <>\n      <div className=\"flex flex-col space-y-3 text-center\">\n        <div className=\"space-y-2\">\n          <h1 className=\"font-semibold text-2xl tracking-tight text-white\">\n            {title}\n          </h1>\n          <p className=\"text-white text-sm leading-relaxed\">{description}</p>\n        </div>\n        {/* Orange accent line */}\n        <div className=\"mx-auto w-16 h-px bg-gradient-to-r from-transparent via-orange-500 to-transparent opacity-60\" />\n      </div>\n      <SignIn fallbackRedirectUrl={fallbackRedirectUrl} />\n      {/* Privacy and Terms text */}\n      <div className=\"text-center text-xs text-muted-foreground mt-4\">\n        By signing in, you agree to our{' '}\n        <a\n          href=\"/legal/terms\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"text-orange-500 hover:text-orange-600 underline transition-colors\"\n        >\n          Terms of Service\n        </a>{' '}\n        and{' '}\n        <a\n          href=\"/legal/privacy\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"text-orange-500 hover:text-orange-600 underline transition-colors\"\n        >\n          Privacy Policy\n        </a>\n        .\n      </div>\n    </>\n  );\n};\n\nexport default SignInPage;\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAAA;AACA;;;;;AAEA,MAAM,QAAQ;AACd,MAAM,cAAc;AAEb,MAAM,WAAqB,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;IAAE;IAAO;AAAY;AAUtE,MAAM,aAAa,OAAO,EAAE,YAAY,EAAmB;IACzD,MAAM,SAAS,MAAM;IAErB,2EAA2E;IAC3E,IAAI,OAAO,SAAS,IAAI,OAAO,KAAK,EAAE;QACpC,CAAA,GAAA,oSAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;IACvE;IAEA,sEAAsE;IACtE,IAAI,sBAAsB;IAC1B,IAAI,OAAO,YAAY,EAAE;QACvB,sBAAsB,CAAC,2BAA2B,EAAE,mBAAmB,OAAO,YAAY,GAAG;IAC/F;IAEA,qBACE;;0BACE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,6VAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAGrD,6VAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,6VAAC,6IAAA,CAAA,SAAM;gBAAC,qBAAqB;;;;;;0BAE7B,6VAAC;gBAAI,WAAU;;oBAAiD;oBAC9B;kCAChC,6VAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;oBAEI;oBAAI;oBACL;kCACJ,6VAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;oBAEG;;;;;;;;;AAKZ;uCAEe", "debugId": null}}]}