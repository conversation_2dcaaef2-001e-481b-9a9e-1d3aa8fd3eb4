{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_65232333._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_5738550d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|images|ingest|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "27c611ea94defdd74b396f46a9a19108", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c0584986a82d5b326310a1e98d2116c4d92fab3ad910597c412b3d8a42b33aae", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ab537528fb6a4b57ed03a91631fe354cfb14410e7ec67f88d0514df254626096"}}}, "instrumentation": null, "functions": {}}