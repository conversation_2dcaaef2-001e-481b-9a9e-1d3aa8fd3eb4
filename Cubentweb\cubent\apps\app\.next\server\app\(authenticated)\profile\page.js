(()=>{var e={};e.id=9945,e.ids=[9945],e.modules={673:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(35371);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,i=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:o}=t,c=Object.keys(i).map(e=>{let t=null==s?void 0:s[e],r=null==o?void 0:o[e];if(null===t)return null;let a=n(t)||n(r);return i[e][a]}),l=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return a(e,c,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:s,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...o,...l}[t]):({...o,...l})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10783:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>N,AvatarFallback:()=>k,AvatarImage:()=>C});var r=s(99730),n=s(57752),a=s(1493),i=s(18526),o=s(62676),c=s(56750),l=s(52578);function d(){return()=>{}}var u="Avatar",[p,m]=(0,a.A)(u),[x,f]=p(u),h=n.forwardRef((e,t)=>{let{__scopeAvatar:s,...a}=e,[i,o]=n.useState("idle");return(0,r.jsx)(x,{scope:s,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,r.jsx)(c.sG.span,{...a,ref:t})})});h.displayName=u;var v="AvatarImage",b=n.forwardRef((e,t)=>{let{__scopeAvatar:s,src:a,onLoadingStatusChange:u=()=>{},...p}=e,m=f(v,s),x=function(e,{referrerPolicy:t,crossOrigin:s}){let r=(0,l.useSyncExternalStore)(d,()=>!0,()=>!1),a=n.useRef(null),i=r?(a.current||(a.current=new window.Image),a.current):null,[c,u]=n.useState(()=>j(i,e));return(0,o.N)(()=>{u(j(i,e))},[i,e]),(0,o.N)(()=>{let e=e=>()=>{u(e)};if(!i)return;let r=e("loaded"),n=e("error");return i.addEventListener("load",r),i.addEventListener("error",n),t&&(i.referrerPolicy=t),"string"==typeof s&&(i.crossOrigin=s),()=>{i.removeEventListener("load",r),i.removeEventListener("error",n)}},[i,s,t]),c}(a,p),h=(0,i.c)(e=>{u(e),m.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==x&&h(x)},[x,h]),"loaded"===x?(0,r.jsx)(c.sG.img,{...p,ref:t,src:a}):null});b.displayName=v;var g="AvatarFallback",y=n.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:a,...i}=e,o=f(g,s),[l,d]=n.useState(void 0===a);return n.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>d(!0),a);return()=>window.clearTimeout(e)}},[a]),l&&"loaded"!==o.imageLoadingStatus?(0,r.jsx)(c.sG.span,{...i,ref:t}):null});function j(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}y.displayName=g;var w=s(83590);function N({className:e,...t}){return(0,r.jsx)(h,{"data-slot":"avatar",className:(0,w.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function C({className:e,...t}){return(0,r.jsx)(b,{"data-slot":"avatar-image",className:(0,w.cn)("aspect-square size-full",e),...t})}function k({className:e,...t}){return(0,r.jsx)(y,{"data-slot":"avatar-fallback",className:(0,w.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13339:(e,t,s)=>{"use strict";s.d(t,{ExtensionConnectionStatus:()=>p});var r=s(99730),n=s(74938),a=s(87785),i=s(57752),o=s(22683),c=s(19161);let l=(0,c.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var d=s(71265);let u=(0,c.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function p({isConnected:e,lastSync:t,activeSessions:s,termsAccepted:c}){let[p,m]=(0,i.useState)(!1),[x,f]=(0,i.useState)(!1),[h,v]=(0,i.useState)(null),[b,g]=(0,i.useState)(new Date),y=async()=>{try{let e=await fetch("/api/extension/status");if(e.ok){let t=await e.json();v(t.status),g(new Date)}}catch(e){console.error("Failed to fetch extension status:",e)}},j=async()=>{f(!0),await y(),f(!1),o.toast.success("Status refreshed")},w=async()=>{if(!c){o.toast.error("Please accept the terms of service first"),window.location.href="/terms";return}m(!0);try{let e=`vscode://cubent.cubent/connect?website=${encodeURIComponent(window.location.origin)}`;window.open(e,"_blank"),o.toast.success("Opening VS Code extension..."),setTimeout(()=>{window.location.reload()},3e3)}catch(e){o.toast.error("Failed to connect extension")}finally{m(!1)}},N=async()=>{try{(await fetch("/api/extension/sessions?all=true",{method:"DELETE"})).ok?(o.toast.success("All extension sessions terminated"),await y()):o.toast.error("Failed to disconnect extension")}catch(e){o.toast.error("Failed to disconnect extension")}},C=async()=>{try{m(!0);let e=await fetch("/api/extension/status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:`test_${Date.now()}`,extensionVersion:"test-button",vscodeVersion:"webapp",platform:"browser"})}),t=await e.json();e.ok?(o.toast.success("Test session created successfully!"),await y(),setTimeout(()=>window.location.reload(),1e3)):o.toast.error(`Test failed: ${t.error||"Unknown error"}`)}catch(e){o.toast.error("Test button failed"),console.error("Test error:",e)}finally{m(!1)}},k=h||{connected:e,lastActive:t?.toISOString()||null,activeSessions:s,health:e?"healthy":"disconnected",sessions:[]};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Status:"}),(0,r.jsxs)(a.E,{variant:(e=>{switch(e){case"healthy":return"default";case"warning":return"secondary";default:return"outline"}})(k.health),className:"flex items-center gap-1",children:["warning"===k.health?(0,r.jsx)(l,{className:"h-3 w-3"}):null,k.connected?"Connected":"Disconnected"]})]}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:j,disabled:x,className:"h-6 w-6 p-0",children:(0,r.jsx)(d.A,{className:`h-3 w-3 ${x?"animate-spin":""}`})})]}),k.connected&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Active Sessions:"}),(0,r.jsx)("span",{className:"text-sm font-mono",children:k.activeSessions})]}),k.lastActive&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Last Active:"}),(0,r.jsx)("span",{className:"text-sm",children:new Date(k.lastActive).toLocaleString()})]}),k.sessions.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Recent Sessions:"}),(0,r.jsx)("div",{className:"space-y-1",children:k.sessions.slice(0,2).map(e=>(0,r.jsxs)("div",{className:"text-xs bg-muted p-2 rounded",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{children:["VS Code ",e.vscodeVersion]}),(0,r.jsx)("span",{children:e.platform})]}),e.extensionVersion&&(0,r.jsxs)("div",{className:"text-muted-foreground",children:["Extension v",e.extensionVersion]})]},e.id))})]})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",b.toLocaleTimeString()]}),(0,r.jsx)("div",{className:"pt-2 space-y-2",children:k.connected?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.$,{onClick:N,variant:"destructive",size:"sm",className:"w-full",children:"Disconnect All Sessions"}),(0,r.jsx)(n.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,r.jsxs)("a",{href:"/profile/extension",className:"flex items-center gap-2",children:[(0,r.jsx)(u,{className:"h-3 w-3"}),"Manage Extension"]})})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.$,{onClick:w,disabled:p||!c,size:"sm",className:"w-full",children:p?"Connecting...":"Connect Extension"}),(0,r.jsx)(n.$,{onClick:C,disabled:p,variant:"outline",size:"sm",className:"w-full",children:"\uD83E\uDDEA Test Connection"})]})}),!c&&(0,r.jsxs)("div",{className:"flex items-start gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800",children:[(0,r.jsx)(l,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-xs",children:[(0,r.jsx)("p",{className:"font-medium text-yellow-800 dark:text-yellow-200",children:"Terms Required"}),(0,r.jsx)("p",{className:"text-yellow-700 dark:text-yellow-300",children:"You must accept the terms of service before connecting the extension."})]})]})]})}},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},18231:(e,t,s)=>{"use strict";s.d(t,{ExtensionConnectionStatus:()=>r});let r=(0,s(6340).registerClientReference)(function(){throw Error("Attempted to call ExtensionConnectionStatus() from the server but ExtensionConnectionStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\components\\extension-connection-status.tsx","ExtensionConnectionStatus")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},24700:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(94752);s(23233);var n=s(30409),a=s(673),i=s(58559);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:s,asChild:a=!1,...c}){let l=a?n.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:e})),...c})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30409:(e,t,s)=>{"use strict";s.d(t,{DX:()=>i});var r=s(23233);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=s(94752),i=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...a}=e;if(r.isValidElement(s)){var i;let e,o,c=(i=s,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),l=function(e,t){let s={...t};for(let r in t){let n=e[r],a=t[r];/^on[A-Z]/.test(r)?n&&a?s[r]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(s[r]=n):"style"===r?s[r]={...n,...a}:"className"===r&&(s[r]=[n,a].filter(Boolean).join(" "))}return{...e,...s}}(a,s.props);return s.type!==r.Fragment&&(l.ref=t?function(...e){return t=>{let s=!1,r=e.map(e=>{let r=n(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():n(e[t],null)}}}}(t,c):c),r.cloneElement(s,l)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:n,...i}=e,o=r.Children.toArray(n),l=o.find(c);if(l){let e=l.props.children,n=o.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...i,ref:s,children:n})});return s.displayName=`${e}.Slot`,s}("Slot"),o=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},31421:e=>{"use strict";e.exports=require("node:child_process")},31711:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>n,AvatarFallback:()=>i,AvatarImage:()=>a});var r=s(6340);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\avatar.tsx","Avatar"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\avatar.tsx","AvatarImage"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\avatar.tsx","AvatarFallback")},33873:e=>{"use strict";e.exports=require("path")},34069:(e,t,s)=>{"use strict";s.d(t,{w:()=>c});var r=s(81121),n=s.n(r);let a="next-forge",i={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,c=({title:e,description:t,image:s,...r})=>{let c=`${e} | ${a}`,l={title:c,description:t,applicationName:a,metadataBase:o?new URL(`https://${o}`):void 0,authors:[i],creator:i.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:c},openGraph:{title:c,description:t,type:"website",siteName:a,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},d=n()(l,r);return s&&d.openGraph&&(d.openGraph.images=[{url:s,width:1200,height:630,alt:e}]),d}},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44218:(e,t,s)=>{Promise.resolve().then(s.bind(s,13339)),Promise.resolve().then(s.bind(s,86332)),Promise.resolve().then(s.t.bind(s,41265,23)),Promise.resolve().then(s.bind(s,22683)),Promise.resolve().then(s.bind(s,10783))},44708:e=>{"use strict";e.exports=require("node:https")},46954:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>i});var r=s(94752);s(23233);var n=s(58559);function a({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},48161:e=>{"use strict";e.exports=require("node:os")},49499:(e,t,s)=>{let{createProxy:r}=s(20867);e.exports=r("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\client\\app-dir\\link.js")},52578:(e,t,s)=>{"use strict";e.exports=s(64959)},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59988:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>r.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>r.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>n.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>r.at});var r=s(54841),n=s(44089)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64959:(e,t,s)=>{"use strict";var r=s(57752),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,i=r.useEffect,o=r.useLayoutEffect,c=r.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!n(e,s)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),r=a({inst:{value:s,getSnapshot:t}}),n=r[0].inst,d=r[1];return o(function(){n.value=s,n.getSnapshot=t,l(n)&&d({inst:n})},[e,s,t]),i(function(){return l(n)&&d({inst:n}),e(function(){l(n)&&d({inst:n})})},[e]),c(s),s};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},70483:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var r=s(94752);s(23233);var n=s(30409),a=s(673),i=s(58559);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:s=!1,...a}){let c=s?n.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...a})}},71265:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19161).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81010:(e,t,s)=>{Promise.resolve().then(s.bind(s,18231)),Promise.resolve().then(s.t.bind(s,21034,23)),Promise.resolve().then(s.t.bind(s,49499,23)),Promise.resolve().then(s.bind(s,93665)),Promise.resolve().then(s.bind(s,31711))},81561:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>l,routeModule:()=>u,tree:()=>c});var r=s(57864),n=s(94327),a=s(70814),i=s(17984),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c={children:["",{children:["(authenticated)",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,91567)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\page.tsx"],d={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(authenticated)/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var r=s(99730);s(57752);var n=s(58576),a=s(72795),i=s(83590);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:s=!1,...a}){let c=s?n.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...a})}},91567:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y,metadata:()=>g});var r=s(94752),n=s(37838),a=s(1359),i=s(18815),o=s(24700),c=s(46954),l=s(70483),d=s(31711),u=s(34069),p=s(62923),m=s(49499),x=s.n(m),f=s(18231);function h({usage:e}){let t,s=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString();return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Tokens Used:"}),(0,r.jsx)("span",{className:"text-sm font-mono",children:s(e.tokensUsed)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Requests Made:"}),(0,r.jsx)("span",{className:"text-sm font-mono",children:s(e.requestsMade)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Cost Accrued:"}),(0,r.jsx)("span",{className:"text-sm font-mono",children:(t=e.costAccrued,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(t))})]}),e.requestsMade>0&&(0,r.jsx)("div",{className:"pt-2 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Avg tokens/request:"}),(0,r.jsx)("span",{className:"text-xs font-mono",children:Math.round(e.tokensUsed/e.requestsMade)})]})})]})}let v="Profile",b="Manage your account and extension settings.",g=(0,u.w)({title:v,description:b}),y=async()=>{let{userId:e}=await (0,n.j)(),t=await (0,a.N)();e&&t||(0,p.redirect)("/sign-in");let s=await i.database.user.upsert({where:{clerkId:e},update:{email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl},create:{clerkId:e,email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl},include:{extensionSessions:{where:{isActive:!0},orderBy:{lastActiveAt:"desc"}},usageMetrics:{orderBy:{date:"desc"},take:30}}}),u=s.extensionSessions.length>0,m=s.usageMetrics.reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0});return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:v}),(0,r.jsx)("p",{className:"text-muted-foreground",children:b})]})}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(c.Zp,{className:"md:col-span-2 lg:col-span-1",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Account Information"}),(0,r.jsx)(c.BT,{children:"Your profile details"})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(d.Avatar,{className:"h-16 w-16",children:[(0,r.jsx)(d.AvatarImage,{src:s.picture||"",alt:s.name||""}),(0,r.jsx)(d.AvatarFallback,{children:s.name?.split(" ").map(e=>e[0]).join("")||"U"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:s.name||"No name set"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:s.email})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Subscription:"}),(0,r.jsx)(l.E,{variant:"ACTIVE"===s.subscriptionStatus?"default":"secondary",children:s.subscriptionTier})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Status:"}),(0,r.jsx)(l.E,{variant:"ACTIVE"===s.subscriptionStatus?"default":"outline",children:s.subscriptionStatus})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Terms Accepted:"}),(0,r.jsx)(l.E,{variant:s.termsAccepted?"default":"destructive",children:s.termsAccepted?"Yes":"No"})]})]})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"VS Code Extension"}),(0,r.jsx)(c.BT,{children:"Connection status and management"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(f.ExtensionConnectionStatus,{isConnected:u,lastSync:s.lastExtensionSync,activeSessions:s.extensionSessions.length,termsAccepted:s.termsAccepted})})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Usage Overview"}),(0,r.jsx)(c.BT,{children:"Last 30 days"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(h,{usage:m})})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)(c.Zp,{children:(0,r.jsxs)(c.Wu,{className:"p-6",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Extension Settings"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Manage your VS Code extension preferences"}),(0,r.jsx)(o.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(x(),{href:"/profile/extension",children:"Manage Extension"})})]})}),(0,r.jsx)(c.Zp,{children:(0,r.jsxs)(c.Wu,{className:"p-6",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Usage Analytics"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"View detailed usage statistics and history"}),(0,r.jsx)(o.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsx)(x(),{href:"/profile/usage",children:"View Usage"})})]})}),(0,r.jsx)(c.Zp,{children:(0,r.jsxs)(c.Wu,{className:"p-6",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Settings"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Configure your preferences and sync settings"}),(0,r.jsx)(o.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsx)(x(),{href:"/profile/settings",children:"Settings"})})]})}),(0,r.jsx)(c.Zp,{children:(0,r.jsxs)(c.Wu,{className:"p-6",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Terms & Privacy"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Review terms of service and privacy policy"}),(0,r.jsx)(o.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsx)(x(),{href:"/terms",children:"View Terms"})})]})})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,3319,2644,277,1988,5432,4841,6904,1121,864,7209,6648],()=>s(81561));module.exports=r})();